using TaleWorlds.Core;
using TaleWorlds.MountAndBlade;
using TaleWorlds.CampaignSystem;
using TaleWorlds.Library;
using TaleWorlds.MountAndBlade.CustomBattle;
using LOL_Core.CampaignBehaviors;
using LOL_Core.HeroSkillSystem.Core;

namespace LOL_Core
{
    public class SubModule : MBSubModuleBase
    {
        protected override void OnSubModuleLoad()
        {
            base.OnSubModuleLoad();

            try
            {
                // 预加载技能模板，确保在所有游戏模式下都可用
                HeroAbilityFactory.LoadTemplates();
            }
            catch (System.Exception ex)
            {
                // 记录错误但不让模组加载失败
                System.Console.WriteLine($"LOL_Core: 模组加载时技能模板预加载失败 - {ex.Message}");
            }
        }

        protected override void OnBeforeInitialModuleScreenSetAsRoot()
        {
            base.OnBeforeInitialModuleScreenSetAsRoot();
        }

        protected override void OnGameStart(Game game, IGameStarter gameStarterObject)
        {
            base.OnGameStart(game, gameStarterObject);

            if (gameStarterObject is CampaignGameStarter campaignStarter)
            {
                AddCampaignBehaviors(campaignStarter);
            }
            // 添加对沙盒模式（CustomGame）的支持
            else if (game.GameType is CustomGame && gameStarterObject is BasicGameStarter)
            {
                // 沙盒模式不需要添加战役行为，但需要确保技能系统正常工作
                // 这里可以添加沙盒模式特定的初始化逻辑
            }
        }

        private void AddCampaignBehaviors(CampaignGameStarter campaignStarter)
        {
            campaignStarter.AddBehavior(new HeroSkillCampaignBehavior());
            campaignStarter.AddBehavior(new HeroSkillDataBehavior());
        }

        public override void OnMissionBehaviorInitialize(Mission mission)
        {
            base.OnMissionBehaviorInitialize(mission);

            try
            {
                if (mission.Mode == MissionMode.Battle ||
                    mission.Mode == MissionMode.Tournament ||
                    mission.Mode == MissionMode.Duel)
                {
                    mission.AddMissionBehavior(new LOL_Core.MissionLogics.HeroSkillMissionLogic());
                }
            }
            catch (System.Exception ex)
            {
                // 记录错误但不让游戏崩溃
                Debug.Print($"LOL_Core: 任务行为初始化失败 - {ex.Message}");
            }
        }

        protected override void InitializeGameStarter(Game game, IGameStarter starterObject)
        {
            base.InitializeGameStarter(game, starterObject);

            try
            {
                HeroAbilityFactory.LoadTemplates();
            }
            catch (System.Exception ex)
            {
                // 记录错误但不让游戏崩溃
                Debug.Print($"LOL_Core: 技能模板加载失败 - {ex.Message}");
            }
        }
    }
}
