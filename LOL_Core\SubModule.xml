<?xml version="1.0" encoding="utf-8"?>
<Module>
  <Name value="LOL_Core"/>
  <Id value="LOL_Core"/>
  <Version value="v1.0.0"/>
  <SingleplayerModule value="true"/>
  <MultiplayerModule value="false"/>
  <DependedModules>
    <DependedModule Id="Native" DependedModuleType="Official" />
    <DependedModule Id="SandBoxCore" DependedModuleType="Official" />
    <DependedModule Id="Sandbox" DependedModuleType="Official" />
    <DependedModule Id="CustomBattle" DependedModuleType="Official" />
    <DependedModule Id="TOR_Core" DependedModuleType="Community" />
  </DependedModules>
  <SubModules>
    <SubModule>
      <Name value="LOL_Core"/>
      <DLLName value="LOL_Core.dll"/>
      <SubModuleClassType value="LOL_Core.SubModule"/>
      <Tags>
        <Tag key="DedicatedServerType" value="none" />
        <Tag key="IsNoRenderModeElement" value="false" />
      </Tags>
    </SubModule>
  </SubModules>
  <Xmls>
    <XmlNode>
      <XmlName id="hero_abilities" path="hero_abilities"/>
      <IncludedGameTypes>
        <GameType value="Campaign" />
        <GameType value="CampaignStoryMode" />
        <GameType value="CustomGame" />
      </IncludedGameTypes>
    </XmlNode>
    <XmlNode>
      <XmlName id="hero_skill_templates" path="hero_skill_templates"/>
      <IncludedGameTypes>
        <GameType value="Campaign" />
        <GameType value="CampaignStoryMode" />
        <GameType value="CustomGame" />
      </IncludedGameTypes>
    </XmlNode>
  </Xmls>
</Module>
